import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";

interface BarData {
  label: string;
  value: number;
  color?: string;
}

interface D3BarChartProps {
  data: BarData[];
  width?: number;
  height?: number;
  title?: string;
  onBarClick?: (bar: BarData) => void;
}

export default function D3BarChart({ 
  data, 
  width = 600, 
  height = 400,
  title = "Bar Chart",
  onBarClick
}: D3BarChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const hoveredBar = useSignal<BarData | null>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous render

    const margin = { top: 20, right: 30, bottom: 60, left: 60 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3.scaleBand()
      .domain(data.map(d => d.label))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.value) || 0])
      .nice()
      .range([innerHeight, 0]);

    // Color scale
    const colorScale = d3.scaleOrdinal()
      .domain(data.map(d => d.label))
      .range(d3.schemeCategory10);

    // Add grid lines
    g.selectAll(".grid-line-y")
      .data(yScale.ticks(5))
      .enter()
      .append("line")
      .attr("class", "grid-line-y")
      .attr("x1", 0)
      .attr("x2", innerWidth)
      .attr("y1", d => yScale(d))
      .attr("y2", d => yScale(d))
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);

    // Add axes
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale)
        .tickFormat(d3.format(".2s"))
        .ticks(5));

    // Style axes
    xAxis.selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .attr("transform", "rotate(-45)")
      .style("text-anchor", "end");

    yAxis.selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280");

    g.selectAll(".domain")
      .style("stroke", "#e5e7eb");

    g.selectAll(".tick line")
      .style("stroke", "#e5e7eb");

    // Add bars
    const bars = g.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.label) || 0)
      .attr("width", xScale.bandwidth())
      .attr("y", innerHeight) // Start from bottom for animation
      .attr("height", 0) // Start with 0 height for animation
      .attr("fill", d => d.color || colorScale(d.label) as string)
      .style("cursor", "pointer")
      .style("opacity", 0.8);

    // Animate bars
    bars.transition()
      .duration(800)
      .delay((d, i) => i * 100)
      .attr("y", d => yScale(d.value))
      .attr("height", d => innerHeight - yScale(d.value));

    // Add hover interactions
    bars
      .on("mouseover", function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .style("opacity", 1)
          .attr("stroke", "#374151")
          .attr("stroke-width", 2);

        hoveredBar.value = d;

        // Position tooltip
        if (tooltipRef.current) {
          const tooltip = d3.select(tooltipRef.current);
          tooltip
            .style("opacity", 1)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        }
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(150)
          .style("opacity", 0.8)
          .attr("stroke", "none");

        hoveredBar.value = null;

        if (tooltipRef.current) {
          d3.select(tooltipRef.current).style("opacity", 0);
        }
      })
      .on("click", function(event, d) {
        onBarClick?.(d);
      });

    // Add value labels on top of bars
    g.selectAll(".bar-label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "bar-label")
      .attr("x", d => (xScale(d.label) || 0) + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.value) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "11px")
      .style("fill", "#374151")
      .style("font-weight", "500")
      .style("opacity", 0)
      .text(d => d3.format(".2s")(d.value))
      .transition()
      .duration(800)
      .delay((d, i) => i * 100 + 400)
      .style("opacity", 1);

    // Add axis labels
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - (innerHeight / 2))
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Value");

    g.append("text")
      .attr("transform", `translate(${innerWidth / 2}, ${innerHeight + margin.bottom - 10})`)
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Category");

  }, [data, width, height]);

  // Server-side rendering placeholder
  if (!IS_BROWSER) {
    return (
      <div 
        style={{ width, height }} 
        class="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300"
      >
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p class="mt-2 text-sm text-gray-500">Loading {title}...</p>
        </div>
      </div>
    );
  }

  return (
    <div class="relative" data-testid="d3-bar-chart">
      {/* Chart Title */}
      {title && (
        <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      
      {/* SVG Chart */}
      <svg 
        ref={svgRef} 
        width={width} 
        height={height}
        class="border border-gray-200 rounded-lg bg-white"
      />
      
      {/* Tooltip */}
      <div
        ref={tooltipRef}
        class="absolute pointer-events-none bg-gray-900 text-white text-xs rounded py-2 px-3 shadow-lg opacity-0 transition-opacity z-10"
        data-testid="chart-tooltip"
      >
        {hoveredBar.value && (
          <div>
            <div class="font-medium">{hoveredBar.value.label}</div>
            <div>Value: {hoveredBar.value.value.toLocaleString()}</div>
          </div>
        )}
      </div>
    </div>
  );
}
